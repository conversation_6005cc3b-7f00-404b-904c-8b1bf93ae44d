import React from 'react';
import { Button } from '@repo/ui/components/button';
import QuestionRenderer from '@/components/survey/questions/QuestionRenderer';
import { type ExitSurveyData, type SurveySection } from '@/services/exitSurveyService';

interface SurveyContentProps {
  isLoading: boolean;
  isSaving: boolean;
  surveyData: ExitSurveyData | null;
  currentSection: SurveySection | null;
  lastSaved: Date;
  onResponseUpdate: (questionId: string, value: any, responseId?: string, shouldDelete?: boolean) => void;
  onCommentUpdate: (questionId: string, comment: string, commentId?: string) => void;
  onNext: () => void;
}

const SurveyContent: React.FC<SurveyContentProps> = ({
  isLoading,
  isSaving: _isSaving,
  surveyData,
  currentSection,
  lastSaved: _lastSaved,
  onResponseUpdate,
  onCommentUpdate,
  onNext
}) => {
  if (isLoading) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen">
        <div className="p-6 space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-3/4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse w-full"></div>
        </div>
      </div>
    );
  }

  if (!surveyData || !currentSection) {
    return (
      <div className="flex-1 bg-white dark:bg-gray-800 min-h-screen flex items-center justify-center">
        <p className="text-gray-500 dark:text-gray-400">No survey data available</p>
      </div>
    );
  }

  const isLastSection = surveyData.sections.findIndex(s => s.id === currentSection.id) === surveyData.sections.length - 1;

  return (
    <div className="flex-1 bg-white dark:bg-gray-800 flex flex-col overflow-hidden">
      {/* Section Header - Red bar for exit survey */}
      <div className="bg-red-100 dark:bg-red-900/20 px-6 py-4 border-b dark:border-gray-700 flex-shrink-0">
        <h2 className="text-lg font-medium text-gray-800 dark:text-white">{currentSection.title}</h2>
      </div>

      {/* Scrollable Questions Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="space-y-8">
          {currentSection.questions.length === 0 ? (
            <p className="text-gray-600 dark:text-gray-400">No questions in this section</p>
          ) : (
            currentSection.questions.map((question, index) => (
              <QuestionRenderer
                key={question.id}
                question={question}
                questionNumber={index + 1}
                onResponseUpdate={onResponseUpdate}
                onCommentUpdate={onCommentUpdate}
              />
            ))
          )}
        </div>
      </div>

      {/* Fixed Footer */}
      <div className="bg-white dark:bg-gray-800 border-t dark:border-gray-700 px-6 py-4 flex-shrink-0">
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {/* Questions left count can be added here if needed */}
          </div>
          <Button
            onClick={onNext}
            className="px-8"
          >
            {isLastSection ? 'Submit Survey' : 'Next Section'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SurveyContent;
