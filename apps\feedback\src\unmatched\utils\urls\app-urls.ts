const AUTH = "/auth";
// const USER = "/user";

type ID = number | string;

const getLauncherUrls = () => {
  const ADMIN = "/launcher/admin";

  return {
    default: "/launcher",
    home: `/launcher/home`,
    admin: {
      default: ADMIN,
      employees: {
        default: `${ADMIN}/employees`,

        // All employees children routes go in this.
      },
      files: {
        default: `${ADMIN}/files`,
        viewFile: (id: string) => `${ADMIN}/file/${id}`
        // All files children routes go in this.
      },
      pairings: {
        default: `${ADMIN}/pairings`,
        viewPairing: (id: string) => `${ADMIN}/pairing/${id}`
        // All files children routes go in this.
      },
      admins: {
        default: `${ADMIN}/admins`,
      },
      rolesPermissions: {
        default: `${ADMIN}/roles-permissions`,
      },
      orgSettings: {
        default: `${ADMIN}/org-settings`,
      },
      textTemplates: {
        default: `${ADMIN}/templates`,
      },
    },
  };
};
const getAdminUrls = () => {
  const ADMIN = "/admin";
  const SURVEY = `${ADMIN}/survey`;
  const CREATE_SURVEY = `${SURVEY}/create`;
  const ANALYTICS = `${ADMIN}/analytics`;
  const REPORTS = `${ADMIN}/reports`;
  // const SETTINGS = `${ADMIN}/settings`;
  return {
    default: ADMIN,
    home: `${ADMIN}/dashboard`,
    survey: {
      default: SURVEY,
      editComments: {
        getQuestions: (id: ID) => `${SURVEY}/comments/${id}`,
        viewComments: (id: ID, qID: ID) => `${SURVEY}/comments/${id}/${qID}`,
      },
      create: {
        getUrl: (id: ID) => `${CREATE_SURVEY}/${id}`,
        getQuestionsUrl: (id: ID, versionId?: ID) => {
          return `${CREATE_SURVEY}/${id}/version/${versionId || 0}/questions`;
        },
        getManageQuestionsUrl: (id: ID, versionId?: ID) => {
          return `${SURVEY}/manage/${id}/version/${versionId || 0}/questions`;
        },
        getParticipantsUrl: (id: ID) => `${CREATE_SURVEY}/${id}/paticipants`,
        getRulesUrl: (id: ID, filter: string) => {
          return `${CREATE_SURVEY}/${id}/rules${filter ? `?filter=${filter}` : ''}`;
        },
        // getPairingsUrl: (id: ID) => `${CREATE_SURVEY}/${id}/pairings`,
        // getUsersUrl: (id: ID) => `${CREATE_SURVEY}/${id}/users`,
        getPreviewUrl: (id: ID, versionId?: ID) =>
          `${CREATE_SURVEY}/${id}/preview/${versionId || 0}`,
        getPublishUrl: (id: ID) => `${CREATE_SURVEY}/${id}/publish`,
        getFAQUrl: (id: ID) => `${CREATE_SURVEY}/${id}/faq`,
        getUpwardReviewUrl: (id: ID) => `${CREATE_SURVEY}/${id}/intro`,
        getSendUrl: (id: ID) => `${CREATE_SURVEY}/${id}/send`,
      },
      getSurveysUrl: (filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${SURVEY}/list${query}`;
      },
      getSurveyUrl: (id: ID, filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${SURVEY}/manage/${id}${query}`;
      },
    },
    email: `${ADMIN}/email`,
    // settings: {
    //   getURL: (filter?: string) => {
    //     const query = filter ? `?filter=${filter}` : "";
    //     return `${SETTINGS}${query}`;
    //   },
    // },
    analytics: {
      default: ANALYTICS,
      people: {
        default: `${ANALYTICS}/people`,
        list: `${ANALYTICS}/people/list`,
        getListView: (id: ID) => `${ANALYTICS}/people/list/${id}`,
        getAnalyticsUrl: (id: ID, filter?: string) => {
          const query = filter ? `?filter=${filter}` : "";
          return `${ANALYTICS}/people/${id}/analyze${query}`;
        },
      },
      geRankingListUrl: () => {
        return `${ANALYTICS}/ranking/list`;
      },
      getAggregateUrl: (filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${ANALYTICS}/aggregate${query}`;
      },
      statistics: {
        default: `${ANALYTICS}/stats`,
        getListUrl: (filter?: string) => {
          const query = filter ? `?filter=${filter}` : "";
          return `${ANALYTICS}/stats/list${query}`;
        },
        getStatisticsUrl: (id: ID, filter?: string) => {
          const query = filter ? `?filter=${filter}` : "";
          return `${ANALYTICS}/stats/survey/${id}${query}`;
        },
      },
      engagement: {
        default: `${ANALYTICS}/engagement`,
        list: `${ANALYTICS}/engagement/list`,
        getListView: (id: ID) => `${ANALYTICS}/engagement/view/${id}`,
        getAnalyticsUrl: (id: ID, filter?: string) => {
          const query = filter ? `?filter=${filter}` : "";
          return `${ANALYTICS}/engagement/view/${id}${query}`;
        },
      },
      exit: {
        default: `${ANALYTICS}/exit`,
        list: `${ANALYTICS}/exit/list`,
        getListView: (id: ID) => `${ANALYTICS}/exit/view/${id}`,
        getAnalyticsUrl: (id: ID, filter?: string) => {
          const query = filter ? `?filter=${filter}` : "";
          return `${ANALYTICS}/exit/view/${id}${query}`;
        },
      },
    },
    reports: {
      default: REPORTS,
      surveyList: `${REPORTS}/surveys`,
      getSurveyReportsUrl: (id: ID, filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${REPORTS}/survey/ind/${id}${query}`;
      },
      getSurveyTemplatesUrl: (id: ID, filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${REPORTS}/survey/email/${id}${query}`;
      },
      getSurvey360ReportsUrl: (id: ID, filter?: string) => {
        const query = filter ? `?filter=${filter}` : "";
        return `${REPORTS}/survey/360/${id}${query}`;
      },
    },
    dataLoad: {
      default: `${ADMIN}/load`,
      create: `${ADMIN}/load/data`,
      getManagePairingsUrl: (id: ID) => `${ADMIN}/load/pairing/${id}`,
      getManageOngoingUrl: (id: ID) => `${ADMIN}/load/ongoing/${id}`,
      getManageEmployeessUrl: (id: ID) => `${ADMIN}/load/employee/${id}`,
    },
  };
};

const appUrls = {
  site: {
    default: "https://www.unmatched.io/",
  },
  faq: "/faq",
  contactUs: "/contact",
  privacy: "/privacy",
  terms: "/terms",
  confidentiality: "/confidentiality",
  auth: {
    // Authentication
    default: AUTH,
    // accountInformation: `${AUTH}/account-information`,
    login: `${AUTH}/login`,
    requestPassword: `${AUTH}/request-password`,
    getMagicLinkUrl: (email = "", token = "") =>
      `${AUTH}/magiclink/${email}/${token}`,
    terms: `${AUTH}/terms`,
    confidentiality: `${AUTH}/confidentiality`,
    privacyPolicy: `${AUTH}/privacy-policy`,
    // Dynamic Urls
    getActivationUrl: (email: string, token: string) =>
      `${AUTH}/activation/${email}/${token}`,
    getResetPasswordUrl: (email: string, token: string) =>
      `${AUTH}/reset-password/${email}/${token}`,
  },
  oidcCallback: '/oidc/callback',
  admin: getAdminUrls(),
  // launcher: "/launcher",
  launcher: getLauncherUrls(),
  logout: "/logout",

};

export default appUrls;
